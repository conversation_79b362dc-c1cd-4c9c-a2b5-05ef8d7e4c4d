import pandas as pd
import json
import openai
from tqdm import tqdm


class OpenAIAgent:
    def __init__(self, api_key, model="gpt-4-0613"):
        self.client = openai.AzureOpenAI(
            azure_endpoint="https://search-va.byteintl.net/gpt/openapi/online/v2/crawl",
            api_version="2024-03-01-preview",
            api_key=api_key
        )
        self.model = model

    def system_message(self, message):
        return message

    def assistant_message(self, message):
        return message

    def user_message(self, message):
        return message

    @staticmethod
    def clean_json_output(output):
        output = output.strip()
        if output.startswith("```json"):
            output = output[7:]
        if output.endswith("```"):
            output = output[:-3]
        cleaned_output = output.strip()

        try:
            json_data = json.loads(cleaned_output)
        except json.JSONDecodeError as e:
            print(f"JSON decoding error: {e}")
            return cleaned_output

        def clean_json(data):
            if isinstance(data, dict):
                return {key: clean_json(value) for key, value in data.items()}
            elif isinstance(data, list):
                return [clean_json(item) for item in data]
            elif isinstance(data, str):
                return "" if data.lower() in ["unknown", "na", "null"] else data
            else:
                return data

        cleaned_json_data = clean_json(json_data)
        return cleaned_json_data

    def run_openai_task(self, system_message, assistant_message, user_message):
        messages = [
            {"role": "system", "content": self.system_message(system_message)},
            {"role": "assistant", "content": self.assistant_message(assistant_message)},
            {"role": "user", "content": self.user_message(user_message)}
        ]

        completion = self.client.chat.completions.create(
            extra_headers={"X-TT-LOGID": "abc"},  # 请务必带上此header，方便定位问题
            model=self.model,
            messages=messages,
            temperature=0,
            frequency_penalty=0,
            presence_penalty=0
        )

        json_data = completion.model_dump_json()
        data_dict = json.loads(json_data)

        result = self.clean_json_output(data_dict['choices'][0]['message']['content'])
        usage_data = data_dict.get('usage', {})
        total_tokens = usage_data.get('total_tokens', 0)
        prompt_tokens = usage_data.get('prompt_tokens', 0)
        completion_tokens = usage_data.get('completion_tokens', 0)

        return result, total_tokens, prompt_tokens, completion_tokens

    def generate_highlight_script(self, product_name, product_description, category, asr_info, country_code='ID'):
        
        # 根据 CSV 中的 country code 进行语言映射
        language_mapping = {
            'ID': 'Indonesian',
            'VN': 'Vietnamese', 
            'MY': 'Malay',
            'TH': 'Thai',
            'PH': 'Filipino',
            'SG': 'English'
        }
        
        language = language_mapping.get(country_code, 'English')
        
        system_message = "You are an assistant for a TikTok live streaming seller. Your task is to generate engaging highlight scripts for live streams based on product information and existing ASR (speech recognition) content."

        assistant_message = f"""
        Please generate highlight scripts based on the following information:

        - **Product Name**: {product_name}
        - **Category**: {category}
        - **Product Description**: {product_description}
        - **Existing ASR Content**: {asr_info}
        - **Target Language**: {language}
        - **Country**: {country_code}

        **Generate Highlight Scripts**:
        Please generate 3-5 highlight scripts that can be used for TikTok live streaming highlights. These scripts should:
        1. Be engaging and attention-grabbing
        2. Highlight key product features and benefits
        3. Include call-to-action elements
        4. Be suitable for the target market and language
        5. Reference or improve upon the existing ASR content when relevant

        The scripts should incorporate elements that work well for live streaming highlights, such as:
        - Product demonstrations
        - Special offers or promotions
        - Urgency and scarcity
        - Social proof
        - Clear calls to action

        Return the output in JSON format like this:
        {{
            "scripts": [
                {{
                    "title": "Script Title 1",
                    "content": "Script content 1"
                }},
                {{
                    "title": "Script Title 2", 
                    "content": "Script content 2"
                }},
                {{
                    "title": "Script Title 3",
                    "content": "Script content 3"
                }}
            ]
        }}
        """

        user_message = f"""
        Product Name: {product_name}
        Category: {category}
        Product Description: {product_description}
        Existing ASR Content: {asr_info}
        Target Language: {language}
        Country: {country_code}

        Please generate 3-5 engaging highlight scripts for TikTok live streaming in {language}. The scripts should be suitable for the {country_code} market and incorporate effective live streaming techniques.

        Return the output in JSON format like this:
        {{
            "scripts": [
                {{
                    "title": "Script Title 1",
                    "content": "Script content 1"
                }},
                {{
                    "title": "Script Title 2",
                    "content": "Script content 2"
                }},
                {{
                    "title": "Script Title 3", 
                    "content": "Script content 3"
                }}
            ]
        }}
        """

        result, total_tokens, prompt_tokens, completion_tokens = self.run_openai_task(system_message, assistant_message, user_message)
        return result, total_tokens, prompt_tokens, completion_tokens


if __name__ == "__main__":
    agent = OpenAIAgent(api_key="X3zrNBdXZ1wQS1EnqMXP4bkFBBMELMPB", model='gpt-4o-2024-08-06')

    # 读取直播高亮数据
    df = pd.read_csv('./data/live_highlight_prompts_scripts_new.csv')
    print(f"读取到 {len(df)} 个直播高亮记录")

    # 测试模式：只处理前5条记录
    test_mode = False  # 设置为 False 处理完整数据集
    if test_mode:
        df = df.head(5)
        print(f"测试模式：只处理前 {len(df)} 条记录")
    
    # 准备存储结果的列表
    results = []
    
    for index, row in tqdm(df.iterrows(), total=len(df), desc="生成直播高亮脚本"):
        try:
            highlight_vid = row['highlight_vid']
            product_id = row['product_id']
            product_name = row['product_name']
            product_desc = row['product_desc']
            category = row['first_category_name']
            asr_info = row['asr_info']
            country_code = row['country']
            
            print(f"\n处理记录 {index + 1}/{len(df)}: {product_name[:50]}... (国家: {country_code})")
            
            # 生成直播高亮脚本
            scripts_result, total_tokens, prompt_tokens, completion_tokens = agent.generate_highlight_script(
                product_name=product_name,
                product_description=product_desc,
                category=category,
                asr_info=asr_info,
                country_code=country_code
            )
            
            # 保存结果
            result_row = {
                'highlight_vid': highlight_vid,
                'product_id': product_id,
                'product_name': product_name,
                'category': category,
                'country_code': country_code,
                'scripts': scripts_result,
                'total_tokens': total_tokens,
                'prompt_tokens': prompt_tokens,
                'completion_tokens': completion_tokens
            }
            results.append(result_row)
            
            print(f"✓ 成功生成脚本，使用 {total_tokens} tokens")
            
        except Exception as e:
            print(f"✗ 处理记录 {product_name} 时出错: {str(e)}")
            # 添加错误记录
            error_row = {
                'highlight_vid': row['highlight_vid'],
                'product_id': row['product_id'],
                'product_name': row['product_name'],
                'category': row['first_category_name'],
                'country_code': row['country'],
                'scripts': f"Error: {str(e)}",
                'total_tokens': 0,
                'prompt_tokens': 0,
                'completion_tokens': 0
            }
            results.append(error_row)
    
    # 保存结果到 output 目录
    output_df = pd.DataFrame(results)
    output_file = './output/live_highlight_scripts.csv'
    output_df.to_csv(output_file, index=False, encoding='utf-8')
    print(f"\n✓ 结果已保存到: {output_file}")
    
    # 同时保存为 JSON 格式以便查看脚本内容
    output_json_file = './output/live_highlight_scripts.json'
    import json
    with open(output_json_file, 'w', encoding='utf-8') as f:
        json.dump(output_df.to_dict('records'), f, indent=2, ensure_ascii=False)
    print(f"✓ JSON 格式结果已保存到: {output_json_file}")
    
    # 打印统计信息
    total_tokens_used = output_df['total_tokens'].sum()
    successful_generations = len(output_df[output_df['total_tokens'] > 0])
    country_stats = output_df['country_code'].value_counts()
    
    print(f"\n=== 统计信息 ===")
    print(f"总记录数: {len(df)}")
    print(f"成功生成脚本: {successful_generations}")
    print(f"总计使用 tokens: {total_tokens_used}")
    print(f"平均每个记录使用 tokens: {total_tokens_used / len(df):.1f}")
    
    print(f"\n=== 国家分布 ===")
    for country, count in country_stats.items():
        print(f"{country}: {count} 个记录")
