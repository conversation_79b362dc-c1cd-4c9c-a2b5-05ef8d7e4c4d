import pandas as pd
import json
import ast
import os

def expand_highlight_scripts(input_file='./output/live_highlight_scripts.csv', 
                            output_file='./output/live_highlight_scripts_expanded.csv'):
    """
    将直播高亮脚本从紧凑格式展开为每个脚本一行的格式
    
    Args:
        input_file (str): 输入的 CSV 文件路径
        output_file (str): 输出的展开版 CSV 文件路径
    """
    
    print(f"正在读取文件: {input_file}")
    
    # 检查输入文件是否存在
    if not os.path.exists(input_file):
        print(f"错误: 输入文件 {input_file} 不存在")
        return False
    
    # 读取现有的结果文件
    df = pd.read_csv(input_file)
    print(f"读取到 {len(df)} 个直播高亮记录")
    
    # 创建一个新的数据结构来存储展开的脚本
    expanded_data = []
    
    for index, row in df.iterrows():
        try:
            # 解析脚本字符串
            scripts_str = row['scripts']
            
            # 处理不同的脚本格式
            if isinstance(scripts_str, str):
                if scripts_str.startswith("Error:"):
                    # 处理错误记录
                    expanded_row = {
                        'highlight_vid': row['highlight_vid'],
                        'product_id': row['product_id'],
                        'product_name': row['product_name'],
                        'category': row['category'],
                        'country_code': row['country_code'],
                        'script_title': 'Error',
                        'script_content': scripts_str,
                        'total_tokens': row['total_tokens'],
                        'prompt_tokens': row['prompt_tokens'],
                        'completion_tokens': row['completion_tokens']
                    }
                    expanded_data.append(expanded_row)
                    continue
                
                scripts_dict = ast.literal_eval(scripts_str)
            else:
                scripts_dict = scripts_str
            
            scripts_list = scripts_dict['scripts']
            
            print(f"处理记录 {index + 1}/{len(df)}: {row['product_name'][:50]}... (国家: {row['country_code']})")
            
            # 为每个脚本创建一行
            for script in scripts_list:
                expanded_row = {
                    'highlight_vid': row['highlight_vid'],
                    'product_id': row['product_id'],
                    'product_name': row['product_name'],
                    'category': row['category'],
                    'country_code': row['country_code'],
                    'script_title': script['title'],
                    'script_content': script['content'],
                    'total_tokens': row['total_tokens'],
                    'prompt_tokens': row['prompt_tokens'],
                    'completion_tokens': row['completion_tokens']
                }
                expanded_data.append(expanded_row)
                
        except Exception as e:
            print(f'✗ 处理记录 {row["product_name"]} 时出错: {e}')
            # 添加错误记录
            error_row = {
                'highlight_vid': row['highlight_vid'],
                'product_id': row['product_id'],
                'product_name': row['product_name'],
                'category': row['category'],
                'country_code': row['country_code'],
                'script_title': 'Parse Error',
                'script_content': f'Error parsing scripts: {str(e)}',
                'total_tokens': row['total_tokens'],
                'prompt_tokens': row['prompt_tokens'],
                'completion_tokens': row['completion_tokens']
            }
            expanded_data.append(error_row)
            continue
    
    # 创建新的 DataFrame
    expanded_df = pd.DataFrame(expanded_data)
    
    # 保存为 CSV
    expanded_df.to_csv(output_file, index=False, encoding='utf-8')
    
    print(f'✓ 展开的脚本已保存到: {output_file}')
    print(f'总共生成了 {len(expanded_df)} 个脚本条目')
    print(f'涵盖 {expanded_df["highlight_vid"].nunique()} 个直播高亮')
    
    # 显示国家分布
    country_counts = expanded_df['country_code'].value_counts()
    print('\n=== 国家分布 ===')
    for country, count in country_counts.items():
        print(f"{country}: {count} 个脚本")
    
    # 显示类别分布
    category_counts = expanded_df['category'].value_counts()
    print('\n=== 类别分布 ===')
    for category, count in category_counts.items():
        print(f"{category}: {count} 个脚本")
    
    # 显示前几行作为预览
    print('\n=== 文件预览 (前3行) ===')
    preview_df = expanded_df.head(3)[['product_name', 'country_code', 'script_title', 'script_content']]
    for idx, row in preview_df.iterrows():
        print(f"\n产品: {row['product_name'][:60]}...")
        print(f"国家: {row['country_code']}")
        print(f"标题: {row['script_title']}")
        print(f"内容: {row['script_content'][:100]}...")
    
    return True

def create_highlight_summary_report(expanded_file='./output/live_highlight_scripts_expanded.csv'):
    """
    创建直播高亮脚本生成的汇总报告
    
    Args:
        expanded_file (str): 展开版 CSV 文件路径
    """
    
    if not os.path.exists(expanded_file):
        print(f"错误: 文件 {expanded_file} 不存在")
        return
    
    df = pd.read_csv(expanded_file)
    
    # 创建汇总报告
    report = []
    report.append("# 直播高亮脚本生成汇总报告\n")
    report.append(f"生成时间: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
    report.append(f"总脚本数: {len(df)}")
    report.append(f"总直播高亮数: {df['highlight_vid'].nunique()}")
    report.append(f"总 tokens 使用: {df.groupby('highlight_vid')['total_tokens'].first().sum()}")
    report.append(f"平均每个高亮 tokens: {df.groupby('highlight_vid')['total_tokens'].first().mean():.1f}\n")
    
    # 按国家统计
    report.append("## 按国家统计")
    country_stats = df.groupby('country_code').agg({
        'highlight_vid': 'nunique',
        'script_content': 'count'
    }).rename(columns={'highlight_vid': '高亮数', 'script_content': '脚本数'})
    
    for country, stats in country_stats.iterrows():
        report.append(f"- {country}: {stats['高亮数']} 个高亮, {stats['脚本数']} 个脚本")
    
    # 按类别统计
    report.append("\n## 按类别统计")
    category_stats = df.groupby('category').agg({
        'highlight_vid': 'nunique',
        'script_content': 'count'
    }).rename(columns={'highlight_vid': '高亮数', 'script_content': '脚本数'})
    
    for category, stats in category_stats.iterrows():
        report.append(f"- {category}: {stats['高亮数']} 个高亮, {stats['脚本数']} 个脚本")
    
    # 保存报告
    report_file = './output/live_highlight_scripts_summary_report.txt'
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write('\n'.join(report))
    
    print(f"✓ 汇总报告已保存到: {report_file}")

if __name__ == "__main__":
    print("=== 直播高亮脚本展开工具 ===\n")
    
    # 展开脚本
    success = expand_highlight_scripts()
    
    if success:
        # 创建汇总报告
        create_highlight_summary_report()
        print("\n✅ 所有任务完成！")
    else:
        print("\n❌ 任务执行失败")
